# 🪟 Native Windows Build - r4w-linux

Erstelle r4w-linux Images direkt unter Windows ohne WSL2 oder Hyper-V!

## 🎯 Voraussetzungen

### System
- Windows 10/11
- Administrator-Rechte
- Mindestens 8 GB freier Speicherplatz

### Software
- **7-Zip** (automatisch erkannt)
  - Download: https://www.7-zip.org/
  - Oder via winget: `winget install 7zip.7zip`

### Kali Linux Image
- Download von: https://www.kali.org/get-kali/#kali-arm
- Datei: `kali-linux-*-raspberry-pi-zero-w.img.xz`
- Im Projektverzeichnis ablegen

## 🚀 Build-Prozess

### 1. PowerShell als Administrator starten
```powershell
# Rechtsklick auf PowerShell → "Als Administrator ausführen"
```

### 2. Zum Projektverzeichnis navigieren
```powershell
cd C:\Pfad\zu\r4w-linux
```

### 3. Build-Script ausführen
```powershell
# Standard-Build
.\scripts\build-r4w-native-windows.ps1

# Mit spezifischem Image
.\scripts\build-r4w-native-windows.ps1 -KaliImage "kali-linux-2025.2-raspberry-pi-zero-w.img.xz"

# Temp-Dateien behalten (für Debugging)
.\scripts\build-r4w-native-windows.ps1 -KeepTempFiles

# Verbose Output
.\scripts\build-r4w-native-windows.ps1 -Verbose
```

## 📦 Output

Nach erfolgreichem Build:
- `r4w-linux.img.xz` - Komprimiertes Image
- `r4w-linux.img.xz.sha256` - Checksumme

## ⚠️ Wichtige Hinweise

### Einschränkungen
Das native Windows Build kann nur die **Boot-Partition** vollständig konfigurieren:
- ✅ SSH aktiviert
- ✅ WLAN-Konfiguration vorbereitet
- ✅ Statische IP-Konfiguration
- ❌ Root-Filesystem (ext4) nicht vollständig modifizierbar

### Vollständige Konfiguration
Für die komplette r4w-Konfiguration nach dem ersten Boot:

```bash
# Auf dem Raspberry Pi ausführen
sudo /home/<USER>/extras/post-boot-setup.sh
```

## 🔧 Troubleshooting

### 7-Zip nicht gefunden
```powershell
# Installation via winget
winget install 7zip.7zip

# Oder manuell von https://www.7-zip.org/
```

### Administrator-Rechte fehlen
```powershell
# PowerShell als Administrator neu starten
# Rechtsklick → "Als Administrator ausführen"
```

### Image kann nicht gemountet werden
- Antivirus-Software temporär deaktivieren
- Windows Defender Echtzeitschutz pausieren
- Andere Disk-Tools schließen

### Disk bleibt gemountet
```powershell
# Alle Disk-Images dismounten
Get-DiskImage | Dismount-DiskImage
```

## 🎯 Workflow

1. **Windows Build** → Basis-Image mit Boot-Konfiguration
2. **Image flashen** → Auf SD-Karte schreiben
3. **Erster Boot** → Pi startet mit SSH und WLAN
4. **Post-Setup** → Vollständige Konfiguration via SSH

```bash
# Nach dem ersten Boot via SSH
ssh kali@************
sudo /home/<USER>/extras/post-boot-setup.sh
sudo reboot
```

## 🔄 Vergleich: Native vs WSL2

| Feature | Native Windows | WSL2 Build |
|---------|----------------|------------|
| Hyper-V erforderlich | ❌ Nein | ✅ Ja |
| WSL2 erforderlich | ❌ Nein | ✅ Ja |
| Boot-Partition | ✅ Vollständig | ✅ Vollständig |
| Root-Filesystem | ⚠️ Eingeschränkt | ✅ Vollständig |
| Post-Boot Setup | ✅ Erforderlich | ❌ Optional |
| Build-Zeit | 🚀 Schneller | 🐌 Langsamer |

## 💡 Tipps

- **SSD empfohlen** für schnellere Build-Zeiten
- **Antivirus pausieren** während des Builds
- **Temp-Dateien behalten** mit `-KeepTempFiles` für Debugging
- **Checksumme prüfen** vor dem Flashen

## 🎉 Erfolg!

Nach erfolgreichem Build und Setup hast du ein vollständig konfiguriertes r4w-linux Image - ohne WSL2 Drama! 🤓
