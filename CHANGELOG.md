# Changelog

Alle wichtigen Änderungen an diesem Projekt werden in dieser Datei dokumentiert.

Das Format basiert auf [Keep a Changelog](https://keepachangelog.com/de/1.0.0/),
und dieses Projekt folgt [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [0.1.0] - 2025-01-13

### Hinzugefügt
- **Windows Build Support**: PowerShell Script für Windows 11 mit WSL2
- **Device-Mapper Fix**: Robustere Mounting-Lösung mit losetup statt kpartx
- **Erweiterte Tool-Suite**: Firefox ESR, Tor, RiseVPN, Aircrack-ng, Hydra, John, SQLMap, Nikto
- **RiseVPN Integration**: Automatische Installation und Konfiguration
- **Tor Browser CLI**: Aliases für anonyme Web-Requests
- **Besseres Error Handling**: Cleanup-Funktionen und Trap-Handler
- **Windows Dokumentation**: Detaillierte Anleitung für Windows-User (docs/WINDOWS_BUILD.md)
- **Autostart Verbesserungen**: RiseVPN und Tor Auto-Connect

### Geändert
- Build-Script von kpartx auf losetup umgestellt (behebt device-mapper Probleme)
- Erweiterte Paket-Liste mit Security-Tools
- Verbesserte Fehlerbehandlung im Build-Prozess
- Version auf 0.1.0 erhöht

### Behoben
- Device-mapper Fehler beim Mounten von Loop-Devices
- Robustere Partition-Erkennung
- Bessere Cleanup-Routinen

## [0.0.1] - 2025-01-13

### Hinzugefügt
- Erstes funktionsfähiges r4w-linux Image für Raspberry Pi Zero W
- Headless WLAN-Setup mit Platzhaltern
- SSH-Zugang mit Dummy-Key vorkonfiguriert
- Deutsche Lokalisierung (de_DE.UTF-8)
- ZSH als Standard-Shell mit Custom-Prompt
- Vorinstallierte Tools: neofetch, htop, tmux, nmap, netcat
- Custom MOTD mit r4w-Branding
- Autostart-Script für Boot-Aktionen
- Build-Script für eigene Images
- SSH-Config für einfachen Zugang via `ssh hack`
- Statische IP-Konfiguration (************)
- Dokumentation und Setup-Anleitungen

### Sicherheit
- SSH-Passwort-Login deaktiviert (nur Key-basiert)
- Dummy-Key muss vor Produktiveinsatz ersetzt werden
- Firewall-Grundkonfiguration

### Bekannte Probleme
- Build-Script benötigt Linux-Umgebung mit kpartx
- Metasploit und erweiterte Tools noch nicht vollständig integriert
