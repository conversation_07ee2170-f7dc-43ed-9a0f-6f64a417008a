@echo off
REM r4w-linux Native Windows Builder Launcher
REM Version: 0.2.0
REM Startet das PowerShell Build-Script mit Administrator-Rechten

title r4w-linux Native Windows Builder

echo.
echo 🐍 r4w-linux Native Windows Builder
echo ===================================
echo.

REM Prüfe Administrator-Rechte
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ Administrator-<PERSON><PERSON><PERSON> verfügbar
    goto :build
) else (
    echo ❌ Administrator-Rechte erforderlich!
    echo.
    echo 💡 Starte diese Datei als Administrator:
    echo    <PERSON>chtsklick → "Als Administrator ausführen"
    echo.
    pause
    exit /b 1
)

:build
echo 🚀 Starte PowerShell Build-Script...
echo.

REM PowerShell Script ausführen
powershell.exe -ExecutionPolicy Bypass -File "scripts\build-r4w-native-windows.ps1"

echo.
echo 📋 Build abgeschlossen!
echo.
pause
