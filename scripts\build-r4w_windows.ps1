# r4w-linux Builder für Windows
# PowerShell Script zum Erstellen eines konfigurierten Kali Linux Images
# Version: 0.1.0
# Autor: r4w-linux project
# Benötigt: WSL2 mit Ubuntu/Debian

param(
    [Parameter(Mandatory=$false)]
    [string]$KaliImage = "",
    
    [Parameter(Mandatory=$false)]
    [switch]$SkipChecks = $false,
    
    [Parameter(Mandatory=$false)]
    [switch]$Verbose = $false
)

$ErrorActionPreference = "Stop"
$version = "0.1.0"

Write-Host "🐍 r4w-linux Builder für Windows v$version" -ForegroundColor Green
Write-Host "=============================================" -ForegroundColor Green
Write-Host ""

# Prüfe ob WSL2 verfügbar ist
Write-Host "🔍 Prüfe WSL2..." -ForegroundColor Yellow
try {
    $wslVersion = wsl --version 2>$null
    if (-not $wslVersion) {
        throw "WSL nicht gefunden"
    }
    Write-Host "✅ WSL2 verfügbar" -ForegroundColor Green
} catch {
    Write-Host "❌ WSL2 nicht gefunden oder nicht konfiguriert!" -ForegroundColor Red
    Write-Host "💡 Installiere WSL2:" -ForegroundColor Yellow
    Write-Host "   1. wsl --install" -ForegroundColor White
    Write-Host "   2. Neustart" -ForegroundColor White
    Write-Host "   3. Ubuntu aus Microsoft Store installieren" -ForegroundColor White
    exit 1
}

# Prüfe ob Ubuntu WSL verfügbar ist
Write-Host "🔍 Prüfe Ubuntu WSL..." -ForegroundColor Yellow
try {
    $wslDistros = wsl --list --quiet
    if (-not ($wslDistros -match "Ubuntu")) {
        throw "Ubuntu WSL nicht gefunden"
    }
    Write-Host "✅ Ubuntu WSL verfügbar" -ForegroundColor Green
} catch {
    Write-Host "❌ Ubuntu WSL nicht gefunden!" -ForegroundColor Red
    Write-Host "💡 Installiere Ubuntu WSL:" -ForegroundColor Yellow
    Write-Host "   wsl --install -d Ubuntu" -ForegroundColor White
    exit 1
}

# Prüfe ob Kali Image vorhanden
Write-Host "🔍 Suche Kali Linux Image..." -ForegroundColor Yellow
if ($KaliImage -eq "") {
    $kaliFiles = Get-ChildItem -Path . -Filter "kali-linux-*-raspberry-pi-zero-w.img.xz" -ErrorAction SilentlyContinue
    if ($kaliFiles.Count -eq 0) {
        Write-Host "❌ Kein Kali Linux Image gefunden!" -ForegroundColor Red
        Write-Host "💡 Lade es von: https://www.kali.org/get-kali/#kali-arm" -ForegroundColor Yellow
        Write-Host "   Dateiname sollte sein: kali-linux-*-raspberry-pi-zero-w.img.xz" -ForegroundColor White
        exit 1
    }
    $KaliImage = $kaliFiles[0].Name
}

if (-not (Test-Path $KaliImage)) {
    Write-Host "❌ Kali Image nicht gefunden: $KaliImage" -ForegroundColor Red
    exit 1
}

Write-Host "✅ Kali Image gefunden: $KaliImage" -ForegroundColor Green

# WSL Build Script erstellen
$wslBuildScript = @"
#!/bin/bash
set -e

echo "🐍 r4w-linux WSL Builder v$version"
echo "=================================="

# Konfiguration
IMG_BASE="$KaliImage"
WORKDIR="r4w_build"
MOUNT_BOOT="`$WORKDIR/boot"
MOUNT_ROOT="`$WORKDIR/root"
OUTPUT_IMG="r4w-linux.img"

# Cleanup function
cleanup() {
    echo "🧹 Cleanup wird ausgeführt..."
    sudo umount "`$MOUNT_BOOT" 2>/dev/null || true
    sudo umount "`$MOUNT_ROOT" 2>/dev/null || true
    if [ -n "`$LOOP_DEV" ]; then
        sudo losetup -d "`$LOOP_DEV" 2>/dev/null || true
    fi
}

trap cleanup EXIT

# Abhängigkeiten prüfen und installieren
echo "📦 Prüfe Abhängigkeiten..."
sudo apt update -qq
sudo apt install -y util-linux xz-utils coreutils

# Workspace vorbereiten
echo "🧹 Workspace aufräumen..."
cleanup
rm -rf "`$WORKDIR"
mkdir -p "`$MOUNT_BOOT" "`$MOUNT_ROOT"

# Image entpacken
echo "📦 Kali Image entpacken..."
xzcat "`$IMG_BASE" > "`$OUTPUT_IMG"

# Partitionen mounten mit losetup
echo "💾 Partitionen mounten..."
LOOP_DEV=`$(sudo losetup --find --show --partscan "`$OUTPUT_IMG")
echo "📍 Loop device: `$LOOP_DEV"

sleep 3

BOOT_DEV="`${LOOP_DEV}p1"
ROOT_DEV="`${LOOP_DEV}p2"

if [ ! -e "`$BOOT_DEV" ] || [ ! -e "`$ROOT_DEV" ]; then
    echo "❌ Partitionen nicht gefunden!"
    ls -la `${LOOP_DEV}* || true
    exit 1
fi

sudo mount "`$BOOT_DEV" "`$MOUNT_BOOT"
sudo mount "`$ROOT_DEV" "`$MOUNT_ROOT"

echo "✅ Partitionen gemountet"

# Boot-Partition konfigurieren
echo "🔧 Boot-Konfiguration..."
sudo touch "`$MOUNT_BOOT/ssh"

# WLAN-Konfiguration
sudo tee "`$MOUNT_BOOT/wpa_supplicant.conf" > /dev/null << 'WLAN_EOF'
country=DE
ctrl_interface=DIR=/var/run/wpa_supplicant GROUP=netdev
update_config=1

network={
    ssid="WLAN_NAME"
    psk="WLAN_PASSWORT"
    key_mgmt=WPA-PSK
}
WLAN_EOF

# Statische IP-Konfiguration
sudo tee "`$MOUNT_BOOT/dhcpcd.conf.append" > /dev/null << 'IP_EOF'
# Statische IP für r4w
interface wlan0
static ip_address=************/24
static routers=***********
static domain_name_servers=******* *******
IP_EOF

# Extras kopieren
echo "📁 Kopiere Extras..."
sudo cp -r extras "`$MOUNT_ROOT/home/<USER>/"
sudo cp dummy-key/id_rsa_r4w.pub "`$MOUNT_ROOT/tmp/"

echo "✅ Build-Vorbereitung abgeschlossen"
"@

Write-Host "🚀 Starte WSL Build..." -ForegroundColor Green
$wslBuildScript | wsl -d Ubuntu bash

Write-Host "✅ WSL Build abgeschlossen!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 Nächste Schritte:" -ForegroundColor Yellow
Write-Host "   1. Image mit Balena Etcher flashen" -ForegroundColor White
Write-Host "   2. WLAN in wpa_supplicant.conf konfigurieren" -ForegroundColor White
Write-Host "   3. SSH-Key ersetzen" -ForegroundColor White
Write-Host "   4. ssh hack" -ForegroundColor White
