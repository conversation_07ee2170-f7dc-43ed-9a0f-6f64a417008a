@echo off
REM r4w-linux Quick Start für Windows
REM Automatisiertes Setup und Build
REM Version: 0.1.0

title r4w-linux Quick Start

echo.
echo 🐍 r4w-linux Quick Start für Windows
echo ====================================
echo.

REM Prüfe ob PowerShell verfügbar
powershell -Command "Get-Host" >nul 2>&1
if errorlevel 1 (
    echo ❌ PowerShell nicht gefunden!
    echo 💡 PowerShell ist erforderlich für r4w-linux
    pause
    exit /b 1
)

echo ✅ PowerShell verfügbar
echo.

REM Prüfe ob WSL installiert ist
echo 🔍 Prüfe WSL2...
wsl --version >nul 2>&1
if errorlevel 1 (
    echo ❌ WSL2 nicht gefunden!
    echo.
    echo 💡 Möchtest du WSL2 jetzt installieren? (j/n)
    set /p install_wsl=
    if /i "%install_wsl%"=="j" (
        echo 📦 Installiere WSL2...
        wsl --install
        echo.
        echo ✅ WSL2 Installation gestartet
        echo 🔄 Bitte starte den Computer neu und führe dieses Script erneut aus
        pause
        exit /b 0
    ) else (
        echo ❌ WSL2 ist erforderlich für r4w-linux
        pause
        exit /b 1
    )
)

echo ✅ WSL2 verfügbar
echo.

REM Prüfe ob Ubuntu WSL installiert ist
echo 🔍 Prüfe Ubuntu WSL...
wsl --list --quiet | findstr /i "Ubuntu" >nul
if errorlevel 1 (
    echo ❌ Ubuntu WSL nicht gefunden!
    echo.
    echo 💡 Möchtest du Ubuntu WSL jetzt installieren? (j/n)
    set /p install_ubuntu=
    if /i "%install_ubuntu%"=="j" (
        echo 📦 Installiere Ubuntu WSL...
        wsl --install -d Ubuntu
        echo.
        echo ✅ Ubuntu WSL Installation gestartet
        echo 💡 Konfiguriere Ubuntu beim ersten Start und führe dann dieses Script erneut aus
        pause
        exit /b 0
    ) else (
        echo ❌ Ubuntu WSL ist erforderlich für r4w-linux
        pause
        exit /b 1
    )
)

echo ✅ Ubuntu WSL verfügbar
echo.

REM Prüfe ob Kali Image vorhanden
echo 🔍 Suche Kali Linux Image...
dir /b "kali-linux-*-raspberry-pi-zero-w.img.xz" >nul 2>&1
if errorlevel 1 (
    echo ❌ Kein Kali Linux Image gefunden!
    echo.
    echo 💡 Lade das Kali Linux ARM Image herunter:
    echo    https://www.kali.org/get-kali/#kali-arm
    echo.
    echo 📁 Speichere es in diesem Ordner:
    echo    %CD%
    echo.
    echo 💡 Dateiname sollte sein: kali-linux-*-raspberry-pi-zero-w.img.xz
    echo.
    echo ❓ Möchtest du den Download-Link öffnen? (j/n)
    set /p open_link=
    if /i "%open_link%"=="j" (
        start https://www.kali.org/get-kali/#kali-arm
    )
    echo.
    echo 🔄 Führe dieses Script erneut aus, nachdem du das Image heruntergeladen hast
    pause
    exit /b 1
)

echo ✅ Kali Linux Image gefunden
echo.

REM Prüfe ob Balena Etcher installiert ist
echo 🔍 Prüfe Balena Etcher...
where balena-etcher >nul 2>&1
if errorlevel 1 (
    echo ⚠️ Balena Etcher nicht gefunden
    echo.
    echo 💡 Balena Etcher wird zum Flashen des Images benötigt
    echo ❓ Möchtest du den Download-Link öffnen? (j/n)
    set /p open_etcher=
    if /i "%open_etcher%"=="j" (
        start https://www.balena.io/etcher/
    )
    echo.
    echo 💡 Du kannst auch später manuell flashen
) else (
    echo ✅ Balena Etcher verfügbar
)

echo.
echo 🚀 Alle Voraussetzungen erfüllt!
echo.
echo ❓ Möchtest du jetzt das r4w-linux Image erstellen? (j/n)
set /p start_build=
if /i "%start_build%"=="j" (
    echo.
    echo 🔨 Starte Build-Prozess...
    echo.
    powershell -ExecutionPolicy Bypass -File ".\scripts\build-r4w_windows.ps1"
    
    if errorlevel 1 (
        echo.
        echo ❌ Build fehlgeschlagen!
        echo 📋 Prüfe die Fehlermeldungen oben
        pause
        exit /b 1
    )
    
    echo.
    echo ✅ Build erfolgreich abgeschlossen!
    echo.
    echo 📋 Nächste Schritte:
    echo    1. SD-Karte mit Balena Etcher flashen
    echo    2. WLAN in wpa_supplicant.conf konfigurieren
    echo    3. SSH-Key ersetzen
    echo    4. Mit 'ssh hack' verbinden
    echo.
    echo ❓ Möchtest du jetzt Balena Etcher starten? (j/n)
    set /p start_etcher=
    if /i "%start_etcher%"=="j" (
        where balena-etcher >nul 2>&1
        if not errorlevel 1 (
            start balena-etcher
        ) else (
            echo ❌ Balena Etcher nicht gefunden
            echo 💡 Installiere es von: https://www.balena.io/etcher/
        )
    )
) else (
    echo.
    echo 💡 Du kannst den Build später mit diesem Befehl starten:
    echo    .\scripts\build-r4w_windows.ps1
)

echo.
echo 📚 Weitere Hilfe:
echo    - Windows Build Guide: docs\WINDOWS_BUILD.md
echo    - Allgemeine Anleitung: README.md
echo    - Tools Dokumentation: docs\TOOLS.md
echo.

pause
