# 🐍 r4w-linux – Headless Kali für Raspberry Pi Zero W

Ein komplett vorbereitetes, deutschsprachiges Kali Linux Image für den Raspberry Pi Zero W v1.1 – designed für Hacker, Nerds & Terminal-Addicts.

**Ausschließlich für Bildungszwecke!!1**

📡 Headless WLAN
🔐 SSH ready ab dem ersten Boot
💥 Tools vorinstalliert
🧠 Custom ZSH, MOTD & ASCII Flair
📦 Ohne Monitor, Tastatur oder Setup-Wizard

---

## 📦 Inhalte

### Image
- `r4w-linux.img.xz` → direkt flashbar
- basiert auf Kali Linux Light für Raspberry Pi Zero W
- Hostname: `r4w`
- Standardnutzer: `kali`
- Passwort: `toor` (bitte ändern)
- Sprache: Deutsch (`de_DE.UTF-8`)
- Tastatur: `de`
- Shell: `zsh` mit Nerdprompt
- SSH aktiviert (`systemctl enable ssh`)
- Statische IP: `************`

### Vorinstallierte Tools

| Tool              | <PERSON>weck                            |
|-------------------|-----------------------------------|
| `neofetch`        | nerdy Systeminfo beim Login       |
| `htop`, `tmux`    | Monitoring + Multi-TTY            |
| `nmap`, `netcat`  | Netzwerk-Analyse & Shells         |
| `msfconsole`      | Metasploit Framework              |
| `socat`, `chisel` | Portforwarding & Tunnels          |
| `rclone`          | Cloud Sync (Mega, GDrive, etc.)   |
| `wireguard-tools` | VPN-Unterstützung                 |
| `sshuttle`        | Poor man's VPN via SSH            |
| `firefox-esr`     | Headless Browser für Web-Tests    |
| `tor`, `torsocks` | Anonyme Verbindungen & Proxying   |
| `proxychains4`    | Traffic durch Proxy-Ketten        |
| `aircrack-ng`     | WLAN Security Testing             |
| `hydra`, `john`   | Password Cracking Tools           |
| `sqlmap`, `nikto` | Web Application Security          |
| `curl`, `wget`    | Standardnetzwekzeuge              |
| `zsh`, `python3`  | einfach Standard halt 😎           |

### 🐍 r4w-tools - Custom Hacking Suite

| Tool              | Zweck                            |
|-------------------|-----------------------------------|
| `r4w`             | Hauptmenü für alle r4w-tools      |
| `r4w-scan`        | Multi-Purpose Web Vuln Scanner    |
| `r4w-xss`         | Advanced XSS Detection            |
| `r4w-recon`       | Automated Reconnaissance          |
| `r4w-payloads`    | Payload Generator & Encoder       |

### 📚 Wordlists & Databases

| Wordlist          | Zweck                            |
|-------------------|-----------------------------------|
| `rockyou.txt`     | 14M+ Passwörter                   |
| `SecLists`        | Umfangreiche Security Listen      |
| `common-passwords`| Basis-Passwort-Liste              |
| `common-usernames`| Standard-Benutzernamen            |

---

## ⚙️ Erste Schritte

### 1. Image flashen

Nutze [Balena Etcher](https://www.balena.io/etcher/) oder:

```bash
xzcat r4w-linux.img.xz | sudo dd bs=4M of=/dev/sdX conv=fsync status=progress
```

---

### 2. WLAN konfigurieren

Nach dem Flashen → SD-Karte wieder in deinen PC stecken
Bearbeite auf der `boot`-Partition die Datei `wpa_supplicant.conf`:

```conf
country=DE
ctrl_interface=DIR=/var/run/wpa_supplicant GROUP=netdev
update_config=1

network={
    ssid="DEIN_WIFI"
    psk="DEIN_PASSWORT"
}
```

💡 Alternativ DHCP-Reservierung im Router setzen auf IP `************` für MAC-Adresse des Pi.

---

### 3. SSH Shortcut einrichten

Bearbeite auf deinem PC die Datei `~/.ssh/config` (unter Windows: `C:\Users\<USER>\.ssh\config`):

```ssh
Host hack
    HostName ************
    User kali
    IdentityFile ~/.ssh/id_r4w
```

Dann kannst du connecten mit:

```bash
ssh hack
```

---

## 🔐 SSH-Key ersetzen

Der Dummy-Key liegt unter:

```bash
/home/<USER>/.ssh/authorized_keys
```

### So ersetzt du ihn:

1. Eigenen Key erstellen:

```bash
ssh-keygen -t rsa -b 4096 -f ~/.ssh/id_r4w
```

2. Public Key kopieren:

```bash
cat ~/.ssh/id_r4w.pub
```

3. Per SD-Karte oder SCP auf den Pi bringen & ersetzen:

```bash
scp ~/.ssh/id_r4w.pub kali@************:/home/<USER>/.ssh/authorized_keys
```

---

## 🐍 r4w-tools Quick Start

Nach dem ersten Login stehen dir die r4w-tools zur Verfügung:

```bash
# Hauptmenü öffnen
r4w

# Oder direkt Tools starten
r4w-scan      # Web Vulnerability Scanner
r4w-xss       # XSS Detection Tool
r4w-recon     # Automated Reconnaissance
r4w-payloads  # Payload Generator

# Beispiel Web-Test Workflow:
r4w-recon     # 1. Target Discovery & Port Scanning
r4w-scan      # 2. Basic Vulnerability Detection
r4w-xss       # 3. Advanced XSS Testing
```

### Tool-Kategorien:
- 🕷️ **Web Application Security** - Scanner, XSS-Checker, Payload-Generator
- 🌐 **Network Reconnaissance** - Automated Recon, Nmap, Masscan
- 🔐 **Password Attacks** - Hydra, John, Hashcat, CrackMapExec
- 📡 **Wireless Security** - Aircrack-ng, Wifite, Reaver
- 🗂️ **Wordlist Management** - rockyou.txt, SecLists, Custom Lists

---

## 🔁 Autostart & Extras

`/home/<USER>/extras/autostart.sh` wird via `crontab -e` beim Boot ausgeführt:

```bash
@reboot /home/<USER>/extras/autostart.sh
```

Du kannst es nutzen für Reverse Shells, Heartbeat-Pings, VPN-Connects etc.

---

## 🛠️ Selbst bauen

### Linux/WSL
```bash
chmod +x build-r4w.sh
sudo ./build-r4w.sh
```

### Windows (PowerShell)
```powershell
.\scripts\build-r4w_windows.ps1
```

**Detaillierte Windows-Anleitung**: [docs/WINDOWS_BUILD.md](docs/WINDOWS_BUILD.md)

### Make-Targets (Linux)
```bash
make build          # Image erstellen
make clean          # Aufräumen
make flash-help      # Flash-Anleitung
```

---


