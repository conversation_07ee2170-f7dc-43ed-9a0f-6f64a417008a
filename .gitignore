# r4w-linux .gitignore

# Build-Artefakte
r4w-linux.img
r4w-linux.img.xz
r4w-linux.img.xz.sha256
r4w_build/

# Kali Linux Images
kali-linux-*.img
kali-linux-*.img.xz

# Release-Pakete
release/

# Temporäre Dateien
*.tmp
*.temp
*.log

# SSH-Keys (echte Keys, nicht Dummy-Keys)
id_rsa*
!dummy-key/

# Backup-Dateien
*.backup
*.bak
*~

# OS-spezifische Dateien
.DS_Store
Thumbs.db
desktop.ini

# Editor-Dateien
.vscode/
.idea/
*.swp
*.swo
*~

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
.env

# Node.js (falls verwendet)
node_modules/
npm-debug.log*

# Logs
*.log
logs/

# Test-Dateien
test_*
*_test

# Persönliche Konfigurationen
config.local
.env.local

# Entwicklungs-Notizen (können entfernt werden wenn nicht mehr benötigt)
projekt.md
verlauf.md
notizen.md
fahrplahn.md