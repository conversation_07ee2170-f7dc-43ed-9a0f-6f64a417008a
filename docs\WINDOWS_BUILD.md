# 🪟 r4w-linux Build unter Windows

Anleitung zum Erstellen des r4w-linux Images unter Windows 11 mit WSL2.

## 📋 Voraussetzungen

### 1. WSL2 installieren

Falls noch nicht vorhanden:

```powershell
# Als Administrator ausführen
wsl --install
```

Nach dem Neustart:

```powershell
# Ubuntu installieren
wsl --install -d Ubuntu
```

### 2. Ubuntu WSL konfigurieren

Beim ersten Start von Ubuntu:
- Benutzername und Passwort festlegen
- System aktualisieren:

```bash
sudo apt update && sudo apt upgrade -y
```

### 3. Kali Linux Image herunterladen

Lade das aktuelle Kali Linux ARM Image herunter:
- **URL**: https://www.kali.org/get-kali/#kali-arm
- **Datei**: `kali-linux-*-raspberry-pi-zero-w.img.xz`
- **Speicherort**: In den r4w-linux Projektordner

## 🔨 Build-Prozess

### 1. Repository klonen

```powershell
git clone https://github.com/ninjazan420/r4w-linux.git
cd r4w-linux
```

### 2. Kali Image in Projektordner kopieren

Das heruntergeladene Kali Image in den Projektordner kopieren:

```powershell
# Beispiel - passe den Pfad an
Copy-Item "C:\Users\<USER>\Downloads\kali-linux-*-raspberry-pi-zero-w.img.xz" .
```

### 3. Build starten

```powershell
# PowerShell als Administrator öffnen
.\scripts\build-r4w_windows.ps1
```

**Oder mit spezifischem Image:**

```powershell
.\scripts\build-r4w_windows.ps1 -KaliImage "kali-linux-2025.2-raspberry-pi-zero-w.img.xz"
```

## 🔧 Troubleshooting

### WSL2 Probleme

**Problem**: WSL nicht gefunden
```powershell
# WSL Status prüfen
wsl --status

# WSL Version prüfen
wsl --version
```

**Problem**: Ubuntu nicht installiert
```powershell
# Verfügbare Distributionen anzeigen
wsl --list --online

# Ubuntu installieren
wsl --install -d Ubuntu
```

### Build-Probleme

**Problem**: Device-mapper Fehler
- Lösung: Das Windows-Script verwendet losetup statt kpartx
- Automatisch durch WSL2 gelöst

**Problem**: Speicherplatz
```bash
# In WSL2 prüfen
df -h
```

**Problem**: Berechtigungen
```powershell
# PowerShell als Administrator starten
# Rechtsklick auf PowerShell → "Als Administrator ausführen"
```

## 📦 Nach dem Build

### 1. Image flashen

**Mit Balena Etcher (empfohlen):**
1. Balena Etcher herunterladen: https://www.balena.io/etcher/
2. `r4w-linux.img.xz` auswählen
3. SD-Karte auswählen
4. Flash!

**Mit PowerShell Script:**
```powershell
.\scripts\flash-image.ps1
```

### 2. WLAN konfigurieren

Nach dem Flashen SD-Karte wieder einlegen und `wpa_supplicant.conf` bearbeiten:

```conf
country=DE
ctrl_interface=DIR=/var/run/wpa_supplicant GROUP=netdev
update_config=1

network={
    ssid="DEIN_WLAN_NAME"
    psk="DEIN_WLAN_PASSWORT"
    key_mgmt=WPA-PSK
}
```

### 3. SSH-Key ersetzen

```powershell
# Neuen SSH-Key erstellen
ssh-keygen -t rsa -b 4096 -f ~/.ssh/id_r4w

# Public Key anzeigen
Get-Content ~/.ssh/id_r4w.pub
```

Den Inhalt dann in `/home/<USER>/.ssh/authorized_keys` auf dem Pi ersetzen.

### 4. SSH-Verbindung testen

SSH-Config erstellen (`C:\Users\<USER>\.ssh\config`):

```ssh
Host hack
    HostName ************
    User kali
    IdentityFile ~/.ssh/id_r4w
```

Verbinden:
```powershell
ssh hack
```

## 🎯 Performance-Tipps

### WSL2 optimieren

**Speicher begrenzen** (`C:\Users\<USER>\.wslconfig`):
```ini
[wsl2]
memory=4GB
processors=2
```

**WSL2 neustarten:**
```powershell
wsl --shutdown
wsl
```

### Build beschleunigen

- **SSD verwenden** für bessere I/O Performance
- **Mehr RAM** für WSL2 zuweisen
- **Antivirus ausschließen** für WSL2 Ordner

## 🚀 Automatisierung

### Batch-Script erstellen

`build-r4w.bat`:
```batch
@echo off
echo 🐍 r4w-linux Auto-Builder
echo ========================

REM Prüfe ob PowerShell verfügbar
powershell -Command "Get-Host" >nul 2>&1
if errorlevel 1 (
    echo ❌ PowerShell nicht gefunden!
    pause
    exit /b 1
)

REM Starte Build
powershell -ExecutionPolicy Bypass -File ".\scripts\build-r4w_windows.ps1"

echo ✅ Build abgeschlossen!
pause
```

### Scheduled Task

Für regelmäßige Builds:
```powershell
# Task erstellen
$action = New-ScheduledTaskAction -Execute "PowerShell.exe" -Argument "-File C:\path\to\r4w-linux\scripts\build-r4w_windows.ps1"
$trigger = New-ScheduledTaskTrigger -Weekly -DaysOfWeek Sunday -At 2AM
Register-ScheduledTask -TaskName "r4w-linux-build" -Action $action -Trigger $trigger
```

## 📚 Weitere Ressourcen

- **WSL2 Dokumentation**: https://docs.microsoft.com/en-us/windows/wsl/
- **Kali Linux ARM**: https://www.kali.org/get-kali/#kali-arm
- **Balena Etcher**: https://www.balena.io/etcher/
- **SSH für Windows**: https://docs.microsoft.com/en-us/windows-server/administration/openssh/openssh_install_firstuse
