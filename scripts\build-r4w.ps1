
# r4w-linux Builder (PowerShell Version)
# Erstellt ein konfiguriertes Kali Linux Image für Raspberry Pi Zero W (Windows 11)
# Author: r4w-linux project

$ErrorActionPreference = "Stop"

$version = "0.0.1"
$imgBase = Get-ChildItem -Path . -Filter "kali-linux-*-raspberry-pi-zero-w.img.xz" -ErrorAction SilentlyContinue | Select-Object -First 1
$workdir = "r4w_build"
$mountBoot = "$workdir\\boot"
$mountRoot = "$workdir\\root"
$outputImg = "r4w-linux.img"

Write-Host "🐍 r4w-linux Builder v$version"
Write-Host "================================"

if (-not $imgBase) {
    Write-Error "❌ Kein Kali Linux Image gefunden! Lade es von: https://www.kali.org/get-kali/#kali-arm"
    exit 1
}

# Tools prüfen
foreach ($tool in @("wsl", "7z", "dd")) {
    if (-not (Get-Command $tool -ErrorAction SilentlyContinue)) {
        Write-Error "❌ '$tool' ist nicht installiert oder nicht im PATH."
        exit 1
    }
}

# Arbeitsverzeichnis vorbereiten
Write-Host "🧹 Workspace aufräumen..."
Remove-Item -Recurse -Force $workdir -ErrorAction SilentlyContinue
New-Item -ItemType Directory -Path $mountBoot, $mountRoot | Out-Null

# Image entpacken
Write-Host "📦 Kali Image entpacken..."
& 7z e $imgBase.FullName "-o$workdir" -y | Out-Null
$imgFile = Get-ChildItem "$workdir" -Filter "*.img" | Select-Object -First 1
if (-not $imgFile) {
    Write-Error "❌ Entpacktes .img nicht gefunden!"
    exit 1
}

# Image vorbereiten: Benutzer muss dieses mit z.B. Pi Imager oder Etcher weiter flashen

Write-Host "📋 Nächste Schritte (manuell):"
Write-Host "1. Image mit Balena Etcher flashen: $($imgFile.FullName)"
Write-Host "2. WLAN konfigurieren: Erstelle 'wpa_supplicant.conf' auf der boot-Partition:"
Write-Host ""
Write-Host @'
country=DE
ctrl_interface=DIR=/var/run/wpa_supplicant GROUP=netdev
update_config=1

network={
    ssid="WLAN_NAME"
    psk="WLAN_PASSWORT"
    key_mgmt=WPA-PSK
}
'@
Write-Host ""
Write-Host "3. Optional: 'ssh' Datei auf boot-Partition anlegen (zum Aktivieren von SSH)"
Write-Host "4. Statische IP konfigurieren in 'dhcpcd.conf.append' auf boot:"
Write-Host @'
# Statische IP für r4w
interface wlan0
static ip_address=************/24
static routers=***********
static domain_name_servers=******* *******
'@
Write-Host ""
Write-Host "5. Nach dem Boot: mit 'ssh kali@************' verbinden"
Write-Host "✅ Viel Erfolg mit r4w-linux auf deinem Pi!"
