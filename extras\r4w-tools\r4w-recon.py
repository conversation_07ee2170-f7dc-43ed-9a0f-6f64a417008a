#!/usr/bin/env python3
# r4w-recon.py: Automated Reconnaissance Tool
# Version: 1.0.0
# Autor: r4w-linux project
# Verwendung: python3 r4w-recon.py

import subprocess
import sys
import os
import time
import json
from urllib.parse import urlparse

class R4WRecon:
    def __init__(self):
        self.target = ""
        self.output_dir = ""
        
    def banner(self):
        print("🐍 r4w-recon v1.0")
        print("==================")
        print("Automated Reconnaissance & Information Gathering")
        print("⚠️  Nur für Bildungszwecke und autorisierte Tests!")
        print("")
        
    def get_target(self):
        while True:
            target = input("🎯 Ziel (Domain/IP): ").strip()
            if target:
                self.target = target
                # Output-Verzeichnis erstellen
                safe_target = target.replace('/', '_').replace(':', '_')
                self.output_dir = f"/tmp/r4w-recon-{safe_target}-{int(time.time())}"
                os.makedirs(self.output_dir, exist_ok=True)
                print(f"📁 Output-Verzeichnis: {self.output_dir}")
                return target
            print("❌ Ziel darf nicht leer sein!")
            
    def run_command(self, command, description, output_file=None):
        print(f"\n🔍 {description}...")
        print(f"💻 {command}")
        
        try:
            if output_file:
                full_path = os.path.join(self.output_dir, output_file)
                result = subprocess.run(command, shell=True, capture_output=True, text=True, timeout=300)
                with open(full_path, 'w') as f:
                    f.write(f"Command: {command}\n")
                    f.write(f"Return Code: {result.returncode}\n")
                    f.write(f"STDOUT:\n{result.stdout}\n")
                    f.write(f"STDERR:\n{result.stderr}\n")
                print(f"✅ Ergebnis gespeichert: {full_path}")
                return result.stdout
            else:
                subprocess.run(command, shell=True, timeout=300)
                print("✅ Abgeschlossen")
        except subprocess.TimeoutExpired:
            print("⏰ Timeout erreicht")
        except Exception as e:
            print(f"❌ Fehler: {e}")
        
        time.sleep(1)
        
    def whois_lookup(self):
        self.run_command(f"whois {self.target}", "WHOIS Lookup", "whois.txt")
        
    def dns_enumeration(self):
        print("\n🌐 DNS Enumeration")
        
        # Basic DNS Records
        dns_types = ['A', 'AAAA', 'MX', 'NS', 'TXT', 'SOA', 'CNAME']
        for record_type in dns_types:
            self.run_command(f"dig {self.target} {record_type} +short", 
                           f"DNS {record_type} Records", f"dns_{record_type.lower()}.txt")
            
        # DNS Zone Transfer Attempt
        self.run_command(f"dig axfr @{self.target} {self.target}", 
                        "DNS Zone Transfer Attempt", "dns_zone_transfer.txt")
        
        # Subdomain Enumeration (basic)
        subdomains = ['www', 'mail', 'ftp', 'admin', 'test', 'dev', 'staging', 'api', 'blog']
        print("\n🔍 Subdomain Enumeration...")
        for sub in subdomains:
            self.run_command(f"dig {sub}.{self.target} +short", 
                           f"Subdomain: {sub}", f"subdomain_{sub}.txt")
            
    def port_scanning(self):
        print("\n🔌 Port Scanning")
        
        # Quick TCP Scan
        self.run_command(f"nmap -sS -T4 --top-ports 1000 {self.target}", 
                        "Quick TCP Port Scan", "nmap_tcp_quick.txt")
        
        # Service Detection
        self.run_command(f"nmap -sV -sC -O {self.target}", 
                        "Service Detection & OS Fingerprinting", "nmap_service_detection.txt")
        
        # UDP Scan (top ports)
        self.run_command(f"nmap -sU --top-ports 100 {self.target}", 
                        "UDP Port Scan", "nmap_udp.txt")
        
    def web_enumeration(self):
        # Prüfe ob HTTP/HTTPS verfügbar
        http_ports = []
        try:
            # Einfache Port-Prüfung
            result = subprocess.run(f"nmap -p 80,443,8080,8443 {self.target}", 
                                  shell=True, capture_output=True, text=True)
            if "80/tcp open" in result.stdout:
                http_ports.append("80")
            if "443/tcp open" in result.stdout:
                http_ports.append("443")
            if "8080/tcp open" in result.stdout:
                http_ports.append("8080")
            if "8443/tcp open" in result.stdout:
                http_ports.append("8443")
        except:
            pass
            
        if not http_ports:
            print("❌ Keine Web-Services gefunden")
            return
            
        print(f"\n🌐 Web Enumeration (Ports: {', '.join(http_ports)})")
        
        for port in http_ports:
            protocol = "https" if port in ["443", "8443"] else "http"
            url = f"{protocol}://{self.target}:{port}"
            
            # Nikto Scan
            self.run_command(f"nikto -h {url}", 
                           f"Nikto Scan ({port})", f"nikto_{port}.txt")
            
            # Directory Enumeration
            if os.path.exists("/usr/share/wordlists/dirb/common.txt"):
                self.run_command(f"dirb {url} /usr/share/wordlists/dirb/common.txt", 
                               f"Directory Enumeration ({port})", f"dirb_{port}.txt")
            
            # Gobuster (if available)
            if os.path.exists("/usr/share/wordlists/SecLists/Discovery/Web-Content/common.txt"):
                self.run_command(f"gobuster dir -u {url} -w /usr/share/wordlists/SecLists/Discovery/Web-Content/common.txt", 
                               f"Gobuster Scan ({port})", f"gobuster_{port}.txt")
                               
    def ssl_analysis(self):
        print("\n🔐 SSL/TLS Analysis")
        
        # SSL Certificate Info
        self.run_command(f"echo | openssl s_client -connect {self.target}:443 2>/dev/null | openssl x509 -text", 
                        "SSL Certificate Analysis", "ssl_cert.txt")
        
        # SSL Cipher Suites
        self.run_command(f"nmap --script ssl-enum-ciphers -p 443 {self.target}", 
                        "SSL Cipher Enumeration", "ssl_ciphers.txt")
        
    def vulnerability_scanning(self):
        print("\n🔍 Vulnerability Scanning")
        
        # Nmap Vulnerability Scripts
        self.run_command(f"nmap --script vuln {self.target}", 
                        "Nmap Vulnerability Scan", "nmap_vulns.txt")
        
        # SMB Enumeration (if port 445 open)
        self.run_command(f"nmap --script smb-enum-* -p 445 {self.target}", 
                        "SMB Enumeration", "smb_enum.txt")
        
    def generate_report(self):
        print(f"\n📋 Generating Report...")
        
        report_file = os.path.join(self.output_dir, "recon_report.txt")
        
        with open(report_file, 'w') as f:
            f.write("🐍 r4w-recon Report\n")
            f.write("===================\n\n")
            f.write(f"Target: {self.target}\n")
            f.write(f"Scan Date: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Output Directory: {self.output_dir}\n\n")
            
            f.write("📁 Generated Files:\n")
            for file in sorted(os.listdir(self.output_dir)):
                if file != "recon_report.txt":
                    file_path = os.path.join(self.output_dir, file)
                    file_size = os.path.getsize(file_path)
                    f.write(f"  - {file} ({file_size} bytes)\n")
                    
            f.write("\n🔍 Quick Summary:\n")
            
            # DNS Summary
            try:
                with open(os.path.join(self.output_dir, "dns_a.txt"), 'r') as dns_file:
                    dns_content = dns_file.read().strip()
                    if dns_content and "STDOUT:" in dns_content:
                        ip_addresses = dns_content.split("STDOUT:\n")[1].split("STDERR:")[0].strip()
                        if ip_addresses:
                            f.write(f"  IP Addresses: {ip_addresses}\n")
            except:
                pass
                
            # Port Summary
            try:
                with open(os.path.join(self.output_dir, "nmap_tcp_quick.txt"), 'r') as nmap_file:
                    nmap_content = nmap_file.read()
                    if "open" in nmap_content:
                        f.write("  Open Ports found in TCP scan\n")
            except:
                pass
                
        print(f"✅ Report erstellt: {report_file}")
        
    def show_menu(self):
        print("\n🔧 Reconnaissance Optionen:")
        print("1. WHOIS Lookup")
        print("2. DNS Enumeration")
        print("3. Port Scanning")
        print("4. Web Enumeration")
        print("5. SSL/TLS Analysis")
        print("6. Vulnerability Scanning")
        print("7. Vollständiger Scan (alle Optionen)")
        print("8. Report generieren")
        print("9. Output-Verzeichnis öffnen")
        print("0. Beenden")
        print("")
        
    def run(self):
        self.banner()
        target = self.get_target()
        
        print(f"\n🎯 Ziel: {target}")
        print(f"📁 Output: {self.output_dir}")
        
        while True:
            self.show_menu()
            choice = input("Auswahl: ").strip()
            
            if choice == '0':
                print("👋 Reconnaissance beendet!")
                print(f"📁 Ergebnisse: {self.output_dir}")
                break
            elif choice == '1':
                self.whois_lookup()
            elif choice == '2':
                self.dns_enumeration()
            elif choice == '3':
                self.port_scanning()
            elif choice == '4':
                self.web_enumeration()
            elif choice == '5':
                self.ssl_analysis()
            elif choice == '6':
                self.vulnerability_scanning()
            elif choice == '7':
                print("🚀 Starte vollständigen Reconnaissance Scan...")
                self.whois_lookup()
                self.dns_enumeration()
                self.port_scanning()
                self.web_enumeration()
                self.ssl_analysis()
                self.vulnerability_scanning()
                self.generate_report()
                print("✅ Vollständiger Scan abgeschlossen!")
            elif choice == '8':
                self.generate_report()
            elif choice == '9':
                print(f"📁 Output-Verzeichnis: {self.output_dir}")
                subprocess.run(f"ls -la {self.output_dir}", shell=True)
            else:
                print("❌ Ungültige Auswahl!")

if __name__ == "__main__":
    recon = R4WRecon()
    try:
        recon.run()
    except KeyboardInterrupt:
        print("\n\n👋 Reconnaissance beendet!")
    except Exception as e:
        print(f"\n❌ Fehler: {e}")
