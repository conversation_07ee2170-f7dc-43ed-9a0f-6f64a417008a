#!/bin/bash
# RiseVPN Installation Script für r4w-linux
# Installiert RiseVPN für anonyme VPN-Verbindungen
# Version: 0.1.0

set -e

echo "🔒 RiseVPN Installation für r4w-linux"
echo "====================================="

# Prüfe ob wir root sind
if [ "$EUID" -ne 0 ]; then
    echo "❌ Dieses Script muss als root ausgeführt werden!"
    echo "💡 Verwende: sudo $0"
    exit 1
fi

# Prüfe Internetverbindung
echo "🌐 Prüfe Internetverbindung..."
if ! ping -c 1 google.com >/dev/null 2>&1; then
    echo "❌ Keine Internetverbindung!"
    exit 1
fi

echo "✅ Internetverbindung verfügbar"

# Abhängigkeiten installieren
echo "📦 Installiere Abhängigkeiten..."
apt update -qq
apt install -y curl wget unzip openvpn

# RiseVPN herunterladen
echo "⬇️ Lade RiseVPN herunter..."
RISEVPN_DIR="/opt/risevpn"
mkdir -p "$RISEVPN_DIR"
cd "$RISEVPN_DIR"

# RiseVPN Client herunterladen (Linux ARM Version)
RISEVPN_URL="https://github.com/RiseVPN/RiseVPN-Linux/releases/latest/download/risevpn-linux-arm.tar.gz"

if wget -q --spider "$RISEVPN_URL"; then
    echo "📥 Lade RiseVPN Client..."
    wget -O risevpn.tar.gz "$RISEVPN_URL"
    tar -xzf risevpn.tar.gz
    rm risevpn.tar.gz
else
    echo "⚠️ RiseVPN ARM Version nicht verfügbar, verwende alternative Installation..."
    
    # Alternative: OpenVPN mit RiseVPN Konfiguration
    echo "🔧 Konfiguriere OpenVPN für RiseVPN..."
    
    # RiseVPN OpenVPN Konfigurationen herunterladen
    mkdir -p /etc/openvpn/risevpn
    
    # Beispiel-Konfiguration erstellen
    cat > /etc/openvpn/risevpn/risevpn-template.ovpn << 'EOF'
# RiseVPN OpenVPN Konfiguration Template
# Ersetze SERVER_ADDRESS und PORT mit echten Werten
client
dev tun
proto udp
remote SERVER_ADDRESS PORT
resolv-retry infinite
nobind
persist-key
persist-tun
ca ca.crt
cert client.crt
key client.key
cipher AES-256-CBC
auth SHA256
comp-lzo
verb 3
EOF

    echo "📝 Template-Konfiguration erstellt: /etc/openvpn/risevpn/risevpn-template.ovpn"
fi

# RiseVPN Wrapper-Script erstellen
echo "🔧 Erstelle RiseVPN Wrapper..."
cat > /usr/local/bin/risevpn << 'EOF'
#!/bin/bash
# RiseVPN Wrapper Script für r4w-linux

RISEVPN_DIR="/opt/risevpn"
OPENVPN_DIR="/etc/openvpn/risevpn"

show_help() {
    echo "🔒 RiseVPN für r4w-linux"
    echo "========================"
    echo ""
    echo "Verwendung:"
    echo "  risevpn connect [config]    - Verbindung herstellen"
    echo "  risevpn disconnect          - Verbindung trennen"
    echo "  risevpn status              - Status anzeigen"
    echo "  risevpn list                - Verfügbare Konfigurationen"
    echo "  risevpn install-config      - Konfiguration installieren"
    echo ""
    echo "Beispiele:"
    echo "  risevpn connect germany     - Mit Deutschland-Server verbinden"
    echo "  risevpn status              - Verbindungsstatus prüfen"
}

connect_vpn() {
    local config="$1"
    
    if [ -z "$config" ]; then
        echo "❌ Keine Konfiguration angegeben!"
        echo "💡 Verwende: risevpn list"
        exit 1
    fi
    
    local config_file="$OPENVPN_DIR/$config.ovpn"
    
    if [ ! -f "$config_file" ]; then
        echo "❌ Konfiguration nicht gefunden: $config"
        echo "💡 Verfügbare Konfigurationen:"
        list_configs
        exit 1
    fi
    
    echo "🔒 Verbinde mit RiseVPN ($config)..."
    sudo openvpn --config "$config_file" --daemon --log /var/log/risevpn.log
    
    sleep 3
    
    if pgrep -f "openvpn.*$config" >/dev/null; then
        echo "✅ VPN-Verbindung hergestellt!"
        echo "🌐 Neue IP-Adresse:"
        curl -s ifconfig.me || echo "IP-Abfrage fehlgeschlagen"
    else
        echo "❌ VPN-Verbindung fehlgeschlagen!"
        echo "📋 Log anzeigen: tail /var/log/risevpn.log"
    fi
}

disconnect_vpn() {
    echo "🔓 Trenne VPN-Verbindung..."
    sudo pkill -f openvpn || true
    sleep 2
    echo "✅ VPN-Verbindung getrennt"
}

show_status() {
    if pgrep -f openvpn >/dev/null; then
        echo "✅ VPN ist verbunden"
        echo "🌐 Aktuelle IP:"
        curl -s ifconfig.me || echo "IP-Abfrage fehlgeschlagen"
        echo ""
        echo "📊 Verbindungsdetails:"
        ps aux | grep openvpn | grep -v grep
    else
        echo "❌ VPN ist nicht verbunden"
        echo "🌐 Aktuelle IP:"
        curl -s ifconfig.me || echo "IP-Abfrage fehlgeschlagen"
    fi
}

list_configs() {
    echo "📋 Verfügbare VPN-Konfigurationen:"
    if [ -d "$OPENVPN_DIR" ]; then
        for config in "$OPENVPN_DIR"/*.ovpn; do
            if [ -f "$config" ]; then
                basename "$config" .ovpn
            fi
        done
    else
        echo "❌ Keine Konfigurationen gefunden"
        echo "💡 Verwende: risevpn install-config"
    fi
}

install_config() {
    echo "📥 RiseVPN Konfiguration installieren"
    echo "====================================="
    echo ""
    echo "💡 Lade deine RiseVPN .ovpn Dateien in diesen Ordner:"
    echo "   $OPENVPN_DIR"
    echo ""
    echo "📋 Oder verwende scp/sftp:"
    echo "   scp deine-config.ovpn kali@************:~/config.ovpn"
    echo "   sudo mv ~/config.ovpn $OPENVPN_DIR/"
    echo ""
    echo "🔐 Stelle sicher, dass die Konfiguration ca.crt, client.crt und client.key enthält"
}

case "$1" in
    connect)
        connect_vpn "$2"
        ;;
    disconnect)
        disconnect_vpn
        ;;
    status)
        show_status
        ;;
    list)
        list_configs
        ;;
    install-config)
        install_config
        ;;
    *)
        show_help
        ;;
esac
EOF

chmod +x /usr/local/bin/risevpn

# Systemd Service für Auto-Connect erstellen
echo "⚙️ Erstelle Systemd Service..."
cat > /etc/systemd/system/risevpn-autoconnect.service << 'EOF'
[Unit]
Description=RiseVPN Auto-Connect Service
After=network-online.target
Wants=network-online.target

[Service]
Type=oneshot
ExecStart=/usr/local/bin/risevpn connect default
RemainAfterExit=yes
User=root

[Install]
WantedBy=multi-user.target
EOF

# Service aktivieren (aber nicht starten)
systemctl daemon-reload
systemctl enable risevpn-autoconnect.service

echo ""
echo "✅ RiseVPN Installation abgeschlossen!"
echo ""
echo "📋 Nächste Schritte:"
echo "   1. RiseVPN Konfiguration installieren:"
echo "      risevpn install-config"
echo ""
echo "   2. VPN-Verbindung testen:"
echo "      risevpn connect [config-name]"
echo ""
echo "   3. Status prüfen:"
echo "      risevpn status"
echo ""
echo "🔧 Auto-Connect aktivieren:"
echo "   sudo systemctl start risevpn-autoconnect"
echo ""
echo "📚 Hilfe anzeigen:"
echo "   risevpn"
echo ""
