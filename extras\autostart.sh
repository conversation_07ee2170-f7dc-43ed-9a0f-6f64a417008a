#!/bin/bash
# r4w-linux Autostart Script
# Wird beim Boot automatisch ausgeführt
# Version: 0.0.1

LOG_FILE="/var/log/r4w-autostart.log"

# Logging-Funktion
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

log "🐍 r4w-linux Autostart gestartet"

# Warten bis Netzwerk verfügbar ist
log "⏳ Warte auf Netzwerkverbindung..."
for i in {1..30}; do
    if ping -c 1 ******* >/dev/null 2>&1; then
        log "✅ Netzwerk verfügbar"
        break
    fi
    sleep 2
done

# System-Info loggen
log "📊 System-Info:"
log "   Hostname: $(hostname)"
log "   IP-Adresse: $(hostname -I | awk '{print $1}')"
log "   Uptime: $(uptime -p)"
log "   Speicher: $(free -h | grep Mem | awk '{print $3"/"$2}')"

# Neofetch beim ersten Login anzeigen
if [ ! -f "/home/<USER>/.r4w_first_run" ]; then
    echo "neofetch" >> /home/<USER>/.zshrc
    touch "/home/<USER>/.r4w_first_run"
    chown kali:kali "/home/<USER>/.r4w_first_run"
    log "🎨 Neofetch für ersten Login konfiguriert"
fi

# RiseVPN Auto-Connect (falls konfiguriert)
if command -v risevpn >/dev/null 2>&1; then
    if [ -f "/etc/openvpn/risevpn/default.ovpn" ]; then
        log "🔒 Starte RiseVPN Auto-Connect..."
        risevpn connect default >/dev/null 2>&1 &
        log "✅ RiseVPN Auto-Connect gestartet"
    else
        log "⚠️ RiseVPN installiert, aber keine default.ovpn gefunden"
    fi
fi

# Tor Service starten
if command -v tor >/dev/null 2>&1; then
    log "🧅 Starte Tor Service..."
    systemctl start tor >/dev/null 2>&1 || true
    log "✅ Tor Service gestartet"
fi

# Hier können weitere Autostart-Aktionen hinzugefügt werden:
# - Reverse Shell Setup
# - Custom VPN-Verbindung
# - Heartbeat-Ping
# - Custom Services
# - SSH Tunnels
# - Monitoring Scripts

log "✅ r4w-linux Autostart abgeschlossen"